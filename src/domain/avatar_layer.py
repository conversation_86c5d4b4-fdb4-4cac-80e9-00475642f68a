#!/usr/bin/env python3
"""
🎭 Reavo Video Creator - 头像层处理模块
Avatar Layer - 智能头像视频定位、缩放、样式和动画处理

支持功能：
- 智能定位：左上、右上、左下、右下、半屏
- 自动缩放：根据画布尺寸自动调整
- 样式效果：圆角、边框、阴影、背景
- 动画效果：淡入淡出、缩放等
"""

import numpy as np
import cv2
from typing import Dict, Any, List, Tuple, Optional
from moviepy import VideoFileClip, VideoClip, CompositeVideoClip, vfx
from dataclasses import dataclass
from enum import Enum

class AvatarPosition(Enum):
    """头像位置枚举"""
    TOP_LEFT = "top_left"
    TOP_RIGHT = "top_right"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_RIGHT = "bottom_right"
    HALF_SCREEN = "half_screen"
    CENTER = "center"

class AvatarSize(Enum):
    """头像尺寸枚举"""
    SMALL = "small"      # 1/6 屏幕
    MEDIUM = "medium"    # 1/4 屏幕
    LARGE = "large"      # 1/3 屏幕
    HALF = "half"        # 1/2 屏幕

@dataclass
class AvatarStyle:
    """头像样式配置"""
    border_width: int = 0
    border_color: str = "#FFFFFF"
    corner_radius: int = 0
    is_circular: bool = False  # 是否为圆形头像
    shadow_blur: int = 0
    shadow_offset: Tuple[int, int] = (0, 0)
    shadow_color: str = "#000000"
    background_color: Optional[str] = None
    opacity: float = 1.0

@dataclass
class AvatarAnimation:
    """头像动画配置"""
    fade_in_duration: float = 0.5
    fade_out_duration: float = 0.3
    entrance_effect: Optional[str] = None  # "slide_in", "zoom_in", "fade_in"
    exit_effect: Optional[str] = None      # "slide_out", "zoom_out", "fade_out"
    # 新增：明确控制淡入淡出时机
    enable_fade_in: Optional[bool] = None   # None=自动判断, True=强制淡入, False=禁用淡入
    enable_fade_out: Optional[bool] = None  # None=自动判断, True=强制淡出, False=禁用淡出

class AvatarLayer:
    """头像层处理器"""
    
    def __init__(self):
        """初始化头像层处理器"""
        # 默认尺寸配置（相对于画布的比例）
        self.size_ratios = {
            AvatarSize.SMALL: 1/6,
            AvatarSize.MEDIUM: 1/4,
            AvatarSize.LARGE: 1/3,
            AvatarSize.HALF: 1/2
        }
        
        # 默认边距（像素）
        self.default_margin = 50
        
        print("✅ 头像层处理器初始化完成")
    
    def create_avatar_clip(
        self,
        avatar_url: str,
        canvas_size: Tuple[int, int],
        duration: float,
        position: AvatarPosition = AvatarPosition.BOTTOM_RIGHT,
        size: AvatarSize = AvatarSize.MEDIUM,
        style: Optional[AvatarStyle] = None,
        animation: Optional[AvatarAnimation] = None,
        resource_manager = None
    ) -> Optional[VideoClip]:
        """
        创建头像视频剪辑
        
        Args:
            avatar_url: 头像视频URL
            canvas_size: 画布尺寸 (width, height)
            duration: 持续时间
            position: 头像位置
            size: 头像尺寸
            style: 样式配置
            animation: 动画配置
            resource_manager: 资源管理器
            
        Returns:
            头像视频剪辑
        """
        try:
            # 获取头像视频文件
            if resource_manager:
                avatar_path = resource_manager.get_resource(avatar_url, "video")
                if not avatar_path:
                    print(f"⚠️  头像视频获取失败: {avatar_url}")
                    return None
            else:
                avatar_path = avatar_url
            
            # 加载头像视频
            avatar_clip = VideoFileClip(str(avatar_path))

            # 获取原始纵横比
            original_aspect_ratio = avatar_clip.w / avatar_clip.h if avatar_clip.h > 0 else None

            # 去除绿幕背景
            avatar_clip = self._remove_green_screen(avatar_clip)

            # 调整时长
            if avatar_clip.duration > duration:
                avatar_clip = avatar_clip.subclipped(0, duration)
            elif avatar_clip.duration < duration:
                # 如果头像视频较短，循环播放
                loops_needed = int(np.ceil(duration / avatar_clip.duration))
                avatar_clip = avatar_clip.with_duration(duration).loop(duration=duration)

            # 计算头像尺寸和位置，保持纵横比
            avatar_size, avatar_position = self._calculate_avatar_layout(
                canvas_size, position, size, original_aspect_ratio
            )
            
            # 调整头像尺寸，确保尺寸有效
            if avatar_size[0] > 0 and avatar_size[1] > 0:
                avatar_clip = avatar_clip.resized(avatar_size)
            else:
                print(f"⚠️  头像尺寸无效: {avatar_size}，使用默认尺寸")
                default_size = (int(canvas_size[0] * 0.25), int(canvas_size[1] * 0.25))
                avatar_clip = avatar_clip.resized(default_size)
                avatar_size = default_size
            
            # 应用样式效果
            if style:
                avatar_clip = self._apply_avatar_style(avatar_clip, style)
            
            # 设置位置
            avatar_clip = avatar_clip.with_position(avatar_position)
            
            # 应用动画效果
            if animation:
                avatar_clip = self._apply_avatar_animation(avatar_clip, animation)
            
            print(f"✅ 头像剪辑创建成功: {position.value}, 尺寸: {avatar_size}")
            return avatar_clip
            
        except Exception as e:
            print(f"❌ 创建头像剪辑失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _calculate_avatar_layout(
        self,
        canvas_size: Tuple[int, int],
        position: AvatarPosition,
        size: AvatarSize,
        original_aspect_ratio: Optional[float] = None
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        计算头像尺寸和位置，保持原始纵横比

        Args:
            canvas_size: 画布尺寸 (width, height)
            position: 头像位置
            size: 头像尺寸
            original_aspect_ratio: 原始视频纵横比 (width/height)

        Returns:
            (头像尺寸, 头像位置)
        """
        canvas_width, canvas_height = canvas_size

        # 计算头像尺寸，保持纵横比
        if position == AvatarPosition.HALF_SCREEN:
            # 半屏模式：占据屏幕一半
            target_width = canvas_width // 2
            target_height = canvas_height

            if original_aspect_ratio:
                # 保持纵横比，以较小的约束为准
                if target_width / original_aspect_ratio <= target_height:
                    avatar_width = target_width
                    avatar_height = int(target_width / original_aspect_ratio)
                else:
                    avatar_height = target_height
                    avatar_width = int(target_height * original_aspect_ratio)
            else:
                avatar_width = target_width
                avatar_height = target_height
        else:
            # 其他位置：根据size参数计算，保持纵横比
            size_ratio = self.size_ratios[size]

            if original_aspect_ratio:
                # 以高度为基准计算，保持纵横比
                avatar_height = int(canvas_height * size_ratio)
                avatar_width = int(avatar_height * original_aspect_ratio)

                # 如果宽度超出限制，以宽度为基准重新计算
                max_width = int(canvas_width * size_ratio)
                if avatar_width > max_width:
                    avatar_width = max_width
                    avatar_height = int(avatar_width / original_aspect_ratio)
            else:
                # 默认方形
                avatar_width = int(canvas_width * size_ratio)
                avatar_height = int(canvas_height * size_ratio)
        
        # 计算位置，确保位置在画布范围内
        if position == AvatarPosition.TOP_LEFT:
            x, y = self.default_margin, self.default_margin
        elif position == AvatarPosition.TOP_RIGHT:
            x = max(0, canvas_width - avatar_width - self.default_margin)
            y = self.default_margin
        elif position == AvatarPosition.BOTTOM_LEFT:
            x = self.default_margin
            y = max(0, canvas_height - avatar_height - self.default_margin)
        elif position == AvatarPosition.BOTTOM_RIGHT:
            x = max(0, canvas_width - avatar_width - self.default_margin)
            y = max(0, canvas_height - avatar_height - self.default_margin)
        elif position == AvatarPosition.HALF_SCREEN:
            x, y = 0, 0  # 半屏从左上角开始
        else:  # CENTER
            x = max(0, (canvas_width - avatar_width) // 2)
            y = max(0, (canvas_height - avatar_height) // 2)

        # 确保位置不会超出画布
        x = min(x, canvas_width - avatar_width) if avatar_width > 0 else 0
        y = min(y, canvas_height - avatar_height) if avatar_height > 0 else 0

        return (avatar_width, avatar_height), (x, y)
    
    def _apply_avatar_style(self, avatar_clip: VideoClip, style: AvatarStyle) -> VideoClip:
        """应用头像样式效果"""
        try:
            # 透明度调整
            if style.opacity < 1.0:
                avatar_clip = avatar_clip.with_opacity(style.opacity)

            # 如果有圆角、边框等复杂样式，使用自定义变换
            if (style.corner_radius > 0 or style.is_circular or style.border_width > 0 or
                style.shadow_blur > 0 or style.background_color):
                avatar_clip = self._apply_advanced_style(avatar_clip, style)

            return avatar_clip

        except Exception as e:
            print(f"⚠️  应用头像样式失败: {e}")
            return avatar_clip

    def _apply_advanced_style(self, avatar_clip: VideoClip, style: AvatarStyle) -> VideoClip:
        """应用高级样式效果（圆角、边框、阴影等）"""
        def style_transform(get_frame, t):
            try:
                frame = get_frame(t)

                # 检查输入帧的形状和数据类型
                if len(frame.shape) != 3 or frame.shape[2] != 3:
                    print(f"⚠️  输入帧形状不正确: {frame.shape}, 期望: (h, w, 3)")
                    return frame

                # 确保输入帧是正确的数据类型
                if frame.dtype != np.uint8:
                    frame = (frame * 255).astype(np.uint8) if frame.max() <= 1.0 else frame.astype(np.uint8)

                # 转换为OpenCV格式
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                h, w = frame_bgr.shape[:2]

                # 创建带透明通道的图像
                frame_rgba = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGBA)

                # 确保数据类型正确
                frame_rgba = frame_rgba.astype(np.uint8)

                # 应用圆角或圆形效果
                if style.corner_radius > 0 or style.is_circular:
                    frame_rgba = self._apply_rounded_corners(
                        frame_rgba,
                        style.corner_radius if not style.is_circular else 0,
                        style.is_circular
                    )

                # 应用边框
                if style.border_width > 0:
                    frame_rgba = self._apply_border(frame_rgba, style.border_width, style.border_color)

                # 转换回RGB - 确保形状正确
                if len(frame_rgba.shape) == 3 and frame_rgba.shape[2] >= 3:
                    frame_bgr_only = frame_rgba[:,:,:3]  # 只取BGR通道
                    frame_rgb = cv2.cvtColor(frame_bgr_only, cv2.COLOR_BGR2RGB)

                    # 确保输出帧的数据类型和范围正确
                    if frame_rgb.dtype != np.uint8:
                        frame_rgb = frame_rgb.astype(np.uint8)

                    # MoviePy期望的是0-255范围的uint8或0-1范围的float
                    return frame_rgb.astype(np.uint8)
                else:
                    print(f"⚠️  frame_rgba形状异常: {frame_rgba.shape}")
                    return frame

            except Exception as e:
                print(f"⚠️  样式变换失败: {e}")
                import traceback
                traceback.print_exc()
                return frame

        return avatar_clip.transform(style_transform)

    def _apply_rounded_corners(self, frame_rgba: np.ndarray, radius: int, is_circular: bool = False) -> np.ndarray:
        """应用圆角或圆形效果"""
        try:
            h, w = frame_rgba.shape[:2]

            # 确保frame_rgba是正确的数据类型和形状
            if len(frame_rgba.shape) != 3 or frame_rgba.shape[2] != 4:
                print(f"⚠️  frame_rgba形状不正确: {frame_rgba.shape}, 期望: (h, w, 4)")
                return frame_rgba

            if frame_rgba.dtype != np.uint8:
                frame_rgba = frame_rgba.astype(np.uint8)

            if is_circular:
                # 圆形头像：以较小的边为直径
                center_x, center_y = w // 2, h // 2
                circle_radius = min(w, h) // 2

                # 创建圆形遮罩
                mask = np.zeros((h, w), dtype=np.uint8)
                cv2.circle(mask, (center_x, center_y), circle_radius, 255, -1)
            else:
                # 圆角矩形
                mask = np.zeros((h, w), dtype=np.uint8)
                cv2.rectangle(mask, (radius, 0), (w-radius, h), 255, -1)
                cv2.rectangle(mask, (0, radius), (w, h-radius), 255, -1)

                # 四个角的圆形
                cv2.circle(mask, (radius, radius), radius, 255, -1)
                cv2.circle(mask, (w-radius, radius), radius, 255, -1)
                cv2.circle(mask, (radius, h-radius), radius, 255, -1)
                cv2.circle(mask, (w-radius, h-radius), radius, 255, -1)

            # 应用遮罩 - 确保数据类型和形状匹配
            alpha_channel = frame_rgba[:,:,3]
            if alpha_channel.shape != mask.shape:
                print(f"⚠️  alpha通道形状不匹配: alpha={alpha_channel.shape}, mask={mask.shape}")
                return frame_rgba

            # 确保数据类型匹配
            alpha_channel = alpha_channel.astype(np.uint8)
            mask_result = cv2.bitwise_and(alpha_channel, mask)
            frame_rgba[:,:,3] = mask_result

            return frame_rgba

        except Exception as e:
            print(f"⚠️  圆角处理失败: {e}, frame_rgba.shape={frame_rgba.shape}, frame_rgba.dtype={frame_rgba.dtype}")
            return frame_rgba

    def _apply_border(self, frame_rgba: np.ndarray, width: int, color: str) -> np.ndarray:
        """应用边框效果"""
        try:
            # 解析颜色
            color_rgb = self._hex_to_rgb(color)
            h, w = frame_rgba.shape[:2]

            # 确保frame_rgba是正确的数据类型
            if frame_rgba.dtype != np.uint8:
                frame_rgba = frame_rgba.astype(np.uint8)

            # 绘制边框 - 注意OpenCV使用BGR格式，所以需要反转RGB
            # 确保颜色值是整数且在有效范围内
            color_bgra = (
                int(color_rgb[2]) & 0xFF,  # B
                int(color_rgb[1]) & 0xFF,  # G
                int(color_rgb[0]) & 0xFF,  # R
                255                        # A
            )

            # 绘制边框
            cv2.rectangle(frame_rgba, (0, 0), (w-1, h-1), color_bgra, width)

            return frame_rgba

        except Exception as e:
            print(f"⚠️  边框绘制失败: {e}, frame_rgba.shape={frame_rgba.shape}, frame_rgba.dtype={frame_rgba.dtype}")
            return frame_rgba

    def _hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _apply_avatar_animation(self, avatar_clip: VideoClip, animation: AvatarAnimation) -> VideoClip:
        """应用头像动画效果"""
        try:
            effects = []

            # 淡入效果 - 使用FadeIn避免形状不匹配问题
            if animation.fade_in_duration > 0:
                effects.append(vfx.FadeIn(animation.fade_in_duration))

            # 淡出效果 - 使用FadeOut避免形状不匹配问题
            if animation.fade_out_duration > 0:
                effects.append(vfx.FadeOut(animation.fade_out_duration))

            # 入场动画
            if animation.entrance_effect:
                avatar_clip = self._apply_entrance_effect(avatar_clip, animation.entrance_effect)

            # 退场动画
            if animation.exit_effect:
                avatar_clip = self._apply_exit_effect(avatar_clip, animation.exit_effect)

            # 应用所有效果
            if effects:
                avatar_clip = avatar_clip.with_effects(effects)

            return avatar_clip

        except Exception as e:
            print(f"⚠️  应用头像动画失败: {e}")
            return avatar_clip

    def _apply_entrance_effect(self, avatar_clip: VideoClip, effect: str) -> VideoClip:
        """应用入场动画效果"""
        if effect == "slide_in":
            # 滑入效果（从右侧滑入）
            def slide_in_transform(get_frame, t):
                frame = get_frame(t)
                if t < 0.5:  # 前0.5秒执行滑入
                    progress = t / 0.5
                    offset_x = int((1 - progress) * frame.shape[1])
                    # 创建偏移后的帧
                    new_frame = np.zeros_like(frame)
                    if offset_x < frame.shape[1]:
                        new_frame[:, :-offset_x] = frame[:, offset_x:]
                    return new_frame
                return frame
            return avatar_clip.transform(slide_in_transform)

        elif effect == "zoom_in":
            # 缩放入场效果
            def zoom_in_transform(get_frame, t):
                frame = get_frame(t)
                if t < 0.5:  # 前0.5秒执行缩放
                    progress = t / 0.5
                    scale = 0.5 + 0.5 * progress  # 从50%缩放到100%
                    h, w = frame.shape[:2]
                    new_h, new_w = int(h * scale), int(w * scale)
                    resized = cv2.resize(frame, (new_w, new_h))

                    # 居中放置
                    new_frame = np.zeros_like(frame)
                    start_y = (h - new_h) // 2
                    start_x = (w - new_w) // 2
                    new_frame[start_y:start_y+new_h, start_x:start_x+new_w] = resized
                    return new_frame
                return frame
            return avatar_clip.transform(zoom_in_transform)

        return avatar_clip

    def _apply_exit_effect(self, avatar_clip: VideoClip, effect: str) -> VideoClip:
        """应用退场动画效果"""
        # 类似入场效果，但在视频结尾执行
        return avatar_clip

    def _remove_green_screen(self, avatar_clip: VideoClip) -> VideoClip:
        """去除绿幕背景，替换为透明背景"""
        try:
            def green_screen_mask(get_frame, t):
                """创建绿幕遮罩"""
                frame = get_frame(t)

                # 转换为HSV色彩空间，更容易检测绿色
                hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV)

                # 定义绿色范围（HSV）
                # 绿幕通常在这个范围内
                lower_green = np.array([35, 40, 40])   # 较低的绿色阈值
                upper_green = np.array([85, 255, 255]) # 较高的绿色阈值

                # 创建绿色遮罩
                mask = cv2.inRange(hsv, lower_green, upper_green)

                # 形态学操作，去除噪点
                kernel = np.ones((3, 3), np.uint8)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

                # 高斯模糊，使边缘更平滑
                mask = cv2.GaussianBlur(mask, (5, 5), 0)

                # 反转遮罩（我们要保留非绿色部分）
                mask = 255 - mask

                return mask.astype(float) / 255.0

            # 应用绿幕遮罩
            mask_clip = avatar_clip.transform(green_screen_mask)
            avatar_clip = avatar_clip.with_mask(mask_clip)

            print(f"✅ 绿幕去除完成")
            return avatar_clip

        except Exception as e:
            print(f"⚠️  绿幕去除失败，使用原始视频: {e}")
            return avatar_clip

    def parse_avatar_config(self, avatar_config: Dict[str, Any]) -> Tuple[AvatarPosition, AvatarSize, AvatarStyle, AvatarAnimation]:
        """
        解析头像配置字典

        Args:
            avatar_config: 头像配置字典

        Returns:
            (位置, 尺寸, 样式, 动画) 配置元组
        """
        # 解析位置
        position_str = avatar_config.get("position", "bottom_right")
        try:
            position = AvatarPosition(position_str)
        except ValueError:
            position = AvatarPosition.BOTTOM_RIGHT

        # 解析尺寸
        size_str = avatar_config.get("size", "medium")
        try:
            size = AvatarSize(size_str)
        except ValueError:
            size = AvatarSize.MEDIUM

        # 解析样式
        style_config = avatar_config.get("style", {})
        style = AvatarStyle(
            border_width=style_config.get("border_width", 0),
            border_color=style_config.get("border_color", "#FFFFFF"),
            corner_radius=style_config.get("corner_radius", 0),
            is_circular=style_config.get("is_circular", False),
            shadow_blur=style_config.get("shadow_blur", 0),
            shadow_offset=tuple(style_config.get("shadow_offset", [0, 0])),
            shadow_color=style_config.get("shadow_color", "#000000"),
            background_color=style_config.get("background_color"),
            opacity=style_config.get("opacity", 1.0)
        )

        # 解析动画
        animation_config = avatar_config.get("animation", {})
        animation = AvatarAnimation(
            fade_in_duration=animation_config.get("fade_in_duration", 0.5),
            fade_out_duration=animation_config.get("fade_out_duration", 0.3),
            entrance_effect=animation_config.get("entrance_effect"),
            exit_effect=animation_config.get("exit_effect"),
            enable_fade_in=animation_config.get("enable_fade_in"),
            enable_fade_out=animation_config.get("enable_fade_out")
        )

        return position, size, style, animation

    def create_smart_avatar_clips(
        self,
        scenes_with_avatar: List[Tuple[int, Any, float, float]],  # (scene_index, scene, start_time, duration)
        canvas_size: Tuple[int, int],
        resource_manager = None
    ) -> List[VideoClip]:
        """
        创建智能淡入淡出的头像剪辑序列

        Args:
            scenes_with_avatar: 包含头像的场景列表 [(场景索引, 场景对象, 开始时间, 持续时间)]
            canvas_size: 画布尺寸
            resource_manager: 资源管理器

        Returns:
            头像剪辑列表
        """
        if not scenes_with_avatar:
            return []

        avatar_clips = []
        fade_duration = 0.5  # 默认淡入淡出时长

        for i, (scene_index, scene, start_time, duration) in enumerate(scenes_with_avatar):
            try:
                # 解析头像配置
                position, size, style, animation = self.parse_avatar_config({
                    "position": scene.avatar.position,
                    "size": scene.avatar.size,
                    "style": scene.avatar.style or {},
                    "animation": scene.avatar.animation or {}
                })

                # 创建基础头像剪辑
                avatar_clip = self.create_avatar_clip(
                    avatar_url=scene.avatar.url,
                    canvas_size=canvas_size,
                    duration=duration,
                    position=position,
                    size=size,
                    style=style,
                    animation=None,  # 不使用原有动画，使用智能动画
                    resource_manager=resource_manager
                )

                if not avatar_clip:
                    continue

                # 判断是否需要淡入淡出 - 支持配置文件明确指定
                # animation配置已经在前面解析过了，直接使用

                # 淡入逻辑：配置优先，然后自动判断
                if animation.enable_fade_in is not None:
                    # 配置文件明确指定
                    need_fade_in = animation.enable_fade_in
                    print(f"    🎛️  配置指定淡入: {need_fade_in}")
                else:
                    # 自动判断
                    need_fade_in = False
                    if i == 0:
                        # 第一个头像，需要淡入
                        need_fade_in = True
                    else:
                        # 检查与前一个头像是否连续
                        prev_scene_index = scenes_with_avatar[i-1][0]
                        if scene_index > prev_scene_index + 1:
                            # 不连续，需要淡入
                            need_fade_in = True
                    print(f"    🤖 自动判断淡入: {need_fade_in}")

                # 淡出逻辑：配置优先，然后自动判断
                if animation.enable_fade_out is not None:
                    # 配置文件明确指定
                    need_fade_out = animation.enable_fade_out
                    print(f"    🎛️  配置指定淡出: {need_fade_out}")
                else:
                    # 自动判断
                    need_fade_out = False
                    if i == len(scenes_with_avatar) - 1:
                        # 最后一个头像，需要淡出
                        need_fade_out = True
                    else:
                        # 检查与下一个头像是否连续
                        next_scene_index = scenes_with_avatar[i+1][0]
                        if next_scene_index > scene_index + 1:
                            # 不连续，需要淡出
                            need_fade_out = True
                    print(f"    🤖 自动判断淡出: {need_fade_out}")

                # 应用淡入淡出效果 - 使用更兼容的实现方式
                effects = []
                if need_fade_in:
                    # 使用 FadeIn 而不是 CrossFadeIn，避免形状不匹配问题
                    effects.append(vfx.FadeIn(fade_duration))
                    print(f"    🎭 头像淡入: 场景{scene_index + 1}")

                if need_fade_out:
                    # 使用 FadeOut 而不是 CrossFadeOut，避免形状不匹配问题
                    effects.append(vfx.FadeOut(fade_duration))
                    print(f"    🎭 头像淡出: 场景{scene_index + 1}")

                if effects:
                    avatar_clip = avatar_clip.with_effects(effects)

                # 设置时间偏移
                avatar_clip = avatar_clip.with_start(start_time)
                avatar_clips.append(avatar_clip)

                print(f"    ✅ 智能头像剪辑: 场景{scene_index + 1}, 淡入={need_fade_in}, 淡出={need_fade_out}")

            except Exception as e:
                print(f"    ❌ 创建头像剪辑失败: 场景{scene_index + 1}, {e}")
                continue

        return avatar_clips

# 全局头像层处理器实例
avatar_layer = AvatarLayer()
