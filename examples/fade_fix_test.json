{"basic": {"output": {"filename": "Fade_Fix_Test", "quality": "1080p"}, "audio": {"background": {"music": null, "volume": 0.2}, "narration": {"volume": 1.0}}}, "intro": {"duration": 2.0, "title": {"text": "淡入淡出修复测试", "style": {"font_size": 72, "color": "#FFFFFF", "position": "center"}}, "background": {"type": "color", "value": "#000000"}, "animation": {"text_effect": "fade_in"}}, "segments": [{"id": "test-segment-1", "title": "配置控制的淡入淡出测试", "scenes": [{"id": "scene-1-force-fade-in", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/14b297fd-ffd2-4740-9dbc-1bb8fa2deb16.mp3", "duration": 3.0}, "caption": {"text": "强制淡入效果（配置指定）", "style": {"font_size": 42, "color": "white"}}, "background": {"type": "color", "value": "#2C3E50"}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/7fda1b05-9d2b-44a1-8b6e-8bca7281feca.mp4", "position": "bottom_right", "size": "medium", "style": {"is_circular": true, "border_width": 3, "border_color": "#FFD700"}, "animation": {"fade_in_duration": 1.0, "enable_fade_in": true, "enable_fade_out": false}}}, {"id": "scene-2-no-fade", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/6d82d05f-8776-4310-b1fe-3acfc46f4751.mp3", "duration": 3.0}, "caption": {"text": "禁用淡入淡出（配置指定）", "style": {"font_size": 42, "color": "white"}}, "background": {"type": "color", "value": "#34495E"}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/0d45bea2-cd65-4944-8403-d33472fce802.mp4", "position": "bottom_right", "size": "medium", "style": {"corner_radius": 15}, "animation": {"enable_fade_in": false, "enable_fade_out": false}}}, {"id": "scene-3-force-fade-out", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5c727cd5-4d4d-468d-8bef-71e90ccef330.mp3", "duration": 3.0}, "caption": {"text": "强制淡出效果（配置指定）", "style": {"font_size": 42, "color": "white"}}, "background": {"type": "color", "value": "#8E44AD"}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/dec911ae-8bce-4598-9780-b9ca9d70f72d.mp4", "position": "bottom_right", "size": "medium", "style": {"is_circular": true, "border_width": 2, "border_color": "#FFFFFF"}, "animation": {"fade_out_duration": 1.0, "enable_fade_in": false, "enable_fade_out": true}}}, {"id": "scene-4-auto-logic", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/14b297fd-ffd2-4740-9dbc-1bb8fa2deb16.mp3", "duration": 3.0}, "caption": {"text": "自动判断逻辑（无配置）", "style": {"font_size": 42, "color": "white"}}, "background": {"type": "color", "value": "#E74C3C"}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/7fda1b05-9d2b-44a1-8b6e-8bca7281feca.mp4", "position": "bottom_right", "size": "medium", "style": {"corner_radius": 10, "border_width": 2, "border_color": "#FFFFFF"}, "animation": {"fade_in_duration": 0.5, "fade_out_duration": 0.5}}}]}], "outro": {"duration": 2.0, "title": {"text": "测试完成", "style": {"font_size": 64, "color": "#FFFFFF", "position": "center"}}, "background": {"type": "color", "value": "#000000"}, "animation": {"text_effect": "fade_out"}}}